version: '3.8'

services:
  # MongoDB Service
  mongodb:
    image: mongo:7.0
    container_name: mionai-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: mionai
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
    networks:
      - mionai-network

  # Redis Service
  redis:
    image: redis:7.2-alpine
    container_name: mionai-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - mionai-network

  # Redis Commander (Web UI for Redis)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: mionai-redis-commander
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      REDIS_HOSTS: local:redis:6379
    depends_on:
      - redis
    networks:
      - mionai-network

  # Mongo Express (Web UI for MongoDB)
  mongo-express:
    image: mongo-express:1.0.2
    container_name: mionai-mongo-express
    restart: unless-stopped
    ports:
      - "8082:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: password
      ME_CONFIG_MONGODB_URL: **************************************/
      ME_CONFIG_BASICAUTH: false
    depends_on:
      - mongodb
    networks:
      - mionai-network

# Volumes for data persistence
volumes:
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local
  redis_data:
    driver: local

# Network for inter-service communication
networks:
  mionai-network:
    driver: bridge
