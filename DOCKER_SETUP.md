# Docker Development Setup

Bu proje için local development ortamında Redis ve MongoDB kullanmak üzere Docker Compose kurulumu.

## Servisler

### Ana Servisler
- **MongoDB**: Port 27017
  - Username: `admin`
  - Password: `password`
  - Database: `mionai`
  
- **Redis**: Port 6379
  - Persistence aktif (AOF)

### Web UI'lar
- **Mongo Express**: http://localhost:8082
  - MongoDB için web arayüzü
  
- **Redis Commander**: http://localhost:8081
  - Redis için web arayüzü

## Kullanım

### Servisleri Başlatma
```bash
# Tüm servisleri arka planda başlat
docker-compose up -d

# Logları takip et
docker-compose logs -f

# Sadece belirli bir servisi başlat
docker-compose up -d mongodb
docker-compose up -d redis
```

### Servisleri Durdurma
```bash
# Tüm servisleri durdur
docker-compose down

# Verileri de sil (dikkatli kullan!)
docker-compose down -v
```

### Durum Kontrolü
```bash
# Çalışan servisleri görüntüle
docker-compose ps

# Servis loglarını görüntüle
docker-compose logs mongodb
docker-compose logs redis
```

## Bağlantı Bilgileri

### MongoDB
```
URI: ****************************************************************
Host: localhost
Port: 27017
Username: admin
Password: password
Database: mionai
```

### Redis
```
URL: redis://localhost:6379
Host: localhost
Port: 6379
```

## Environment Variables

`.env.example` dosyasını `.env` olarak kopyalayıp kullanabilirsiniz:

```bash
cp .env.example .env
```

## Veri Kalıcılığı

Veriler Docker volume'larda saklanır:
- `mongodb_data`: MongoDB verileri
- `mongodb_config`: MongoDB konfigürasyonu
- `redis_data`: Redis verileri

## Troubleshooting

### Port Çakışması
Eğer portlar kullanımdaysa, `docker-compose.yml` dosyasında port numaralarını değiştirebilirsiniz.

### Veri Sıfırlama
```bash
# Tüm verileri sil ve yeniden başlat
docker-compose down -v
docker-compose up -d
```

### Container'ları Yeniden Oluşturma
```bash
# Container'ları yeniden oluştur
docker-compose up -d --force-recreate
```
