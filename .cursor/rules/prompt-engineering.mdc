---
description: 
globs: *.md
alwaysApply: false
---

<role platform="BrocaAgent" name="Prompt Engineering Guide">
Guide for creating and managing AI prompts in the BrocaAgent language learning platform.
</role>

<principles>
- Design prompts for creative AI responses within defined parameters
- Support 90+ languages through template variables and multilingual architecture
- Use positive, directive language that guides rather than restricts
- Balance comprehensive specification with implementation flexibility
- Enable modular composition through shared components and placeholders
</principles>

<format_foundation>

<tsonl_structure>

All BrocaAgent prompts produce TSONL (Type-String Object Notation Line) output format.

TSON syntax:

- Numbers: `#123` (integer), `=123.45` (float)
- Boolean: `?true`, `?false`
- Null: `~`
- Strings: `"escaped content"`
- Arrays: `[item1 item2]` (space-separated)
- Objects: `{key1"value1" key2#123}` (space-separated)

TSONL format:

- Direct type syntax: `type"content"`
- Complex data: `type{key1"value1" key2"value2"}`
- One valid TSON object per line

</tsonl_structure>

<modular_architecture>

Prompt system consists of:

1. Generator templates in [tools/prompts/generators/](mdc:tools/prompts/generators)
2. Shared components in [tools/prompts/shared/](mdc:tools/prompts/shared)
3. Configuration manifest [tools/prompts/parts.json](mdc:tools/prompts/parts.json)
4. Assembly script [tools/combine.js](mdc:tools/combine.js)

Shared components use `{{{key}}}` placeholder syntax and are referenced via XML tags in checklists.

</modular_architecture>

</format_foundation>

<prompt_structure>

<standard_sections>

Required sections for all prompts:

1. `<role>` - AI identity and purpose
2. `<principles>` - Core behavioral guidelines
3. `<input_understanding>` - Expected input structure
4. `<output_tools>` - Available output types with descriptions
5. Shared component references: `{{{tsonl}}}`, `{{{ref}}}`, etc.
6. `<success_checklist>` - Validation requirements
7. `{{{last}}}` - Standard closing

</standard_sections>

<output_specifications>

Each output type requires:

- Clear purpose and usage context
- TSONL format specification
- Practical examples in `<example></example>` tags
- Field descriptions and constraints

Output types support multilingual content through target language placeholders: `{{target_language}}`

</output_specifications>

</prompt_structure>

<creation_guidelines>

<structural_principles>

- Use XML tags for major sections
- Reference shared components via placeholders
- Maintain clean, minimal markdown (headers, bullets, inline code only)
- Place examples within `<example></example>` tags
- Structure content to encourage natural adherence to requirements

</structural_principles>

<content_principles>

- Write all prompts in English for consistency
- Design for multilingual support through template variables
- Separate user-facing content (target language) from system-facing content (English)
- Focus on capability description and tool explanation
- Provide clear guidance while preserving creative flexibility

</content_principles>

<reference_system>

XML tag mappings for checklist references:

- `{{{tsonl}}}` → `<tsonl>` (format validation)
- `{{{html}}}` → `<html_text_guidelines>` (HTML content rules)
- `{{{ssml}}}` → `<ssml_guidelines>` (audio content rules)
- `{{{dict_ref}}}` → `<dictionary_referencing>` (word reference system)
- `{{{doc_ref}}}` → `<doc_referencing>` (documentation reference system)
- `{{{facing}}}` → `<facing>` (language direction rules)

</reference_system>

</creation_guidelines>

<implementation_process>

<new_prompt_workflow>

1. Study existing generators: [words.md](mdc:tools/prompts/generators/words.md), [tutor.md](mdc:tools/prompts/generators/tutor.md), [coordinator.md](mdc:tools/prompts/generators/coordinator.md)
2. Define role and core principles clearly
3. Specify input/output in TSON format
4. Include relevant shared components via placeholders
5. Add component mappings to [parts.json](mdc:tools/prompts/parts.json)
6. Create comprehensive checklist referencing XML tags

</new_prompt_workflow>

<quality_standards>

- Maintain consistent TSONL syntax throughout
- Use lowercase type names: "text", "picture", "audio"
- Balance comprehensive specification with creative freedom
- Validate format compliance through systematic checklists
- Test multilingual compatibility through template variables

</quality_standards>

</implementation_process>

<reference_examples>

Best structured prompt examples:

- [words.md](mdc:tools/prompts/generators/words.md) - Perfect TSONL structure and output types
- [tutor.md](mdc:tools/prompts/generators/tutor.md) - Clean format and example usage
- [coordinator.md](mdc:tools/prompts/generators/coordinator.md) - Comprehensive guidelines

Additional references:

- [Documentation Generator](mdc:tools/prompts/generators/documentation.md)
- [Grapheme to Phonemes](mdc:tools/prompts/generators/grapheme_to_phonemes.md)
- [Phoneme to Graphemes](mdc:tools/prompts/generators/phoneme_to_graphemes.md)

</reference_examples>
