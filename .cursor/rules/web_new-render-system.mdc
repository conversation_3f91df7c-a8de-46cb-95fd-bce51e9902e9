---
alwaysApply: false
description: Guide for using the new web_new render system
globs: src/web_new/**/*
---

# Web_New Render System Guide

Bu yeni render sistemi template-based bir yaklaşım kullanır ve eski web/ sisteminden farklıdır.

## Sistem Mimarisi

### 1. ComponentFunction

```typescript
export type ComponentFunction<T = any, S = ComponentSlots> = (
  args: T,
  slots?: S
) => ComponentResult;
```

**ÖNEMLI**: ComponentFunction artık HtmlBuilder parametresi almaz! Sadece args ve slots alır.

### 2. ComponentResult

```typescript
export type ComponentResult =
  | {
      inner: string;
      styles?: string[];
      scripts?: string[];
    }
  | HtmlBuilder
  | string;
```

### 3. Template Sistemi

Templates `src/web_new/templates/` dizininde `.html` dosyaları olarak saklanır.

**Template Kullanımı:**

```typescript
// Component içinde
const builder = new HtmlBuilder();
builder.addTemplate("template-name", { key: "value" });
```

**Template Formatı:**

```html
<!-- templates/example.html -->
<div class="card">
  <h3>{{title}}</h3>
  <p>{{description}}</p>
  <img src="{{imageUrl}}" alt="{{imageAlt}}" />
</div>
```

### 4. HtmlBuilder Kullanımı

**Temel kullanım:**

```typescript
const builder = new HtmlBuilder();
builder.setTitle("Page Title");
builder.addMeta("description", "Page description");
builder.addStyle("base.css"); // Asset dosyası
builder.addTemplate("component-name", data);
builder.addComponent(componentResult);
```

**Render:**

```typescript
return builder.render(); // Complete HTML document
```

### 5. Page Implementasyonu

**PageBase sınıfından extend et:**

```typescript
export class MyPage extends PageBase<MyDataType> {
  override async getData(req, res, next): Promise<MyDataType> {
    // Data fetch logic
  }

  override getCacheKey(
    req,
    res,
    next
  ): { key: string; params?: Record<string, string> } {
    return { key: "my-page", params: { id: req.params.id } };
  }

  override render(data: MyDataType): string {
    const builder = new HtmlBuilder();
    // Build page
    return builder.render();
  }
}
```

### 6. Component Yapısı

**Component dosyaları `src/web_new/components/` altında:**

```typescript
// components/my-component.ts
export interface MyComponentProps {
  title: string;
  description: string;
}

export const myComponent: ComponentFunction<MyComponentProps> = (args) => {
  const builder = new HtmlBuilder();
  builder.addTemplate("my-template", args);
  return builder;
};
```

**Template dosyası `src/web_new/templates/` altında:**

```html
<!-- templates/my-template.html -->
<div class="my-component">
  <h2>{{title}}</h2>
  <p>{{description}}</p>
</div>
```

## CSS Stratejisi

### 1. CSS Dosya Organizasyonu

- **base.css**: Temel reset, variables, global styles
- **component-name.css**: Spesifik component stilleri
- Her component kendi CSS dosyasına sahip olabilir

### 2. CSS Kullanımı

```typescript
// HtmlBuilder'da
builder.addStyle("base.css");
builder.addStyle("component-name.css");
```

### 3. Minimal CSS Yaklaşımı

- CSS custom properties (--variables) kullan
- Reusable class'lar yaz
- Minimal ve clean approach benimse

## Asset Yönetimi

### 1. AssetLoader

```typescript
AssetLoader.getCSS("filename.css"); // Minified CSS
AssetLoader.getScript("filename.js"); // Minified JS
AssetLoader.getIcon("icon-name"); // Optimized SVG
AssetLoader.getIconAsDataURL("icon-name"); // Data URL format
```

### 2. Image Handling

**img.ts componentinde handle edilmeli:**

- URL transformations
- Fallbacks
- Size variants (?v=s, ?v=m)
- Base URL handling

## Kurallar

1. **Template-first approach**: Mümkün olduğunca template kullan
2. **MINIMAL CSS**: Bu site sadece siyah-beyaz-gri tonlarında, minimal tasarım. Gereksiz CSS yazma, her satır para kaybı!
3. **Component CSS separation**: Componentler kendi stillerini ekler, sayfa onları eklemesin. Hepsi otomatik birleşip inline olur
4. **Reusable components**: Tekrar kullanılabilir componentler yaz
5. **Clean structure**: Her component kendi template'i ve CSS'i olsun
6. **Performance**: Cache sistemi kullan, gzip compression aktif
7. **Type safety**: TypeScript interface'lerini doğru kullan
8. **ASLA GEREKSIZ CSS YAZMA**: Eski CSS'ler 36-250 satır, yeniler bu kadar olmalı!
9. **İMAGE COMPONENT KULLAN**: Raw img element yazma, her zaman imgComponent kullan!

## Örnek Component Flow

1. **Interface tanımla** → `ComponentProps`
2. **Template oluştur** → `templates/component-name.html`
3. **Component function yaz** → `components/component-name.ts`
4. **CSS ekle** → `assets/component-name.css` (gerekirse)
5. **Page'de kullan** → `builder.addComponent(componentName(props))`

Bu sistem eskisinden daha basit ve template odaklıdır. HtmlBuilder artık secondary role'de, template system primary approach.
