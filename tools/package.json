{"name": "mionai-tools", "version": "1.0.0", "main": "dist/app.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsgo tsc -p tsconfig.json", "build_srv": "node --max-old-space-size=4096 ./node_modules/.bin/tsc -p tsconfig.json", "build_tsc": "tsc -p tsconfig.json", "build_tsgo": "tsgo tsc -p tsconfig.json", "lint": "tslint -p tsconfig.json", "update": "npm install", "build-models": "node ./tools/build_models.js", "combine": "node ./tools/combine.js", "dev": "node --watch dist/app.js", "start": "node ./tools/combine.js && node dist/app.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"dotenv": "^16.4.7", "mongodb": "^6.15.0", "ioredis": "^5.6.1"}, "devDependencies": {"@types/mongodb": "^4.0.6", "@types/node": "^22.14.0", "glob": "11.0.1", "md5": "2.3.0"}}