const fs = require("fs");
const path = require("path");

// model-name to ModelName
function modelName(modelFileName) {
  if (modelFileName.startsWith("_")) {
    let n = modelFileName.split(".")[0].substring(1);
    return n
      .split("-")
      .map((s) => s.charAt(0).toUpperCase() + s.substring(1))
      .join("");
  }

  return modelFileName
    .split(".")[0]
    .split("-")
    .map((s) => s.charAt(0).toUpperCase() + s.substring(1))
    .join("");
}

function buildModels() {
  console.log("Building models in:", __dirname);
  const modelsDir = path.join(__dirname, "../src/models");
  let processedModels = []; // Array of { sortKey: string, exportBlock: string }

  const createExportData = (fileName, subDirectory = "") => {
    // Always skip any file named _index.ts to prevent self-reference or issues
    if (fileName === "_index.ts") {
      console.log(`Skipping file: ${path.join(subDirectory || ".", fileName)}`);
      return null;
    }

    console.log(
      "Processing model file:",
      path.join(subDirectory || ".", fileName)
    );

    const baseNameWithoutExtension = fileName.split(".")[0];
    // For sorting, use a consistent relative path format like "user" or "auth/token"
    const sortKeyPath = subDirectory
      ? `${subDirectory}/${baseNameWithoutExtension}`
      : baseNameWithoutExtension;
    // For the actual import statement path, e.g., "./user" or "./auth/token"
    const importPathString = subDirectory
      ? `./${subDirectory}/${baseNameWithoutExtension}`
      : `./${baseNameWithoutExtension}`;

    const name = modelName(fileName); // Calculate name once (e.g., User, GlobalDoc)
    let exportBlockString;

    if (fileName.startsWith("_")) {
      // Handles files like _internal-config.ts
      // modelName("_internal-config.ts") returns "InternalConfig"
      exportBlockString = `export {\n    IModel as I${name}\n} from "${importPathString}";`;
    } else {
      // Handles regular model files like user.ts or global-doc.ts
      exportBlockString = `export {\n    Model as ${name},\n    IModel as I${name}\n} from "${importPathString}";`;
    }

    return {
      sortKey: sortKeyPath.toLowerCase(), // Sort by full path, case-insensitive
      block: exportBlockString,
    };
  };

  try {
    const entries = fs.readdirSync(modelsDir, { withFileTypes: true });

    for (const entry of entries) {
      if (entry.isFile()) {
        // Process files directly in modelsDir
        const modelData = createExportData(entry.name, "");
        if (modelData) {
          processedModels.push(modelData);
        }
      } else if (entry.isDirectory()) {
        const subdirName = entry.name;
        const subdirPath = path.join(modelsDir, subdirName);
        try {
          const subDirEntries = fs.readdirSync(subdirPath, {
            withFileTypes: true,
          });
          for (const subEntry of subDirEntries) {
            if (subEntry.isFile()) {
              // Process files in the subdirectory
              const modelData = createExportData(subEntry.name, subdirName);
              if (modelData) {
                processedModels.push(modelData);
              }
            }
            // Per user request: "Sadece bir alt klasöre bakılsın"
            // No recursion for directories within subdirectories.
          }
        } catch (error) {
          console.error(
            `Error reading or processing subdirectory ${subdirPath}: ${error.message}`
          );
          // Optionally, decide if this should halt the process or just skip the subdir
        }
      }
    }

    // Sort the processed models by their sortKey (path)
    processedModels.sort((a, b) => a.sortKey.localeCompare(b.sortKey));

    // Generate the final _index.ts content
    // Each export block is already a complete string with a semicolon.
    // Join them with two newlines for separation.
    let finalIndexContent = processedModels.map((p) => p.block).join("\n\n");

    // Add a single newline at the very end of the file if there's content and it doesn't already end with one.
    if (processedModels.length > 0) {
      // Or check if finalIndexContent is not empty
      finalIndexContent += "\n";
    }

    const indexPath = path.join(modelsDir, "_index.ts");
    fs.writeFileSync(indexPath, finalIndexContent);
    console.log(`Successfully updated: ${indexPath}`);
  } catch (error) {
    console.error(`Failed to build models: ${error.message}`);
  }
}

buildModels();
