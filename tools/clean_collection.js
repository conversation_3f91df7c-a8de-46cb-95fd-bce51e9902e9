const { MongoClient } = require("mongodb");
const Redis = require("ioredis");

async function cleanCollections(collections) {
  const uri = "mongodb://localhost:27017";
  const dbName = "mionai"; // assuming this is your database name
  const redisClient = new Redis(); // Default connection to localhost:6379

  const client = new MongoClient(uri);

  try {
    await client.connect();
    console.log("Connected to MongoDB");

    const db = client.db(dbName);

    // Clean MongoDB Collections
    for (const collection of collections) {
      try {
        let collectionName, filter;

        // Check if collection is in {name, filter} format or just a string
        if (typeof collection === "object" && collection.name) {
          collectionName = collection.name;
          filter = collection.filter || {};
        } else {
          collectionName = collection;
          filter = {};
        }

        const result = await db.collection(collectionName).deleteMany(filter);
        console.log(
          `Successfully deleted ${
            result.deletedCount
          } documents from MongoDB collection: ${collectionName}${
            Object.keys(filter).length > 0
              ? ` with filter: ${JSON.stringify(filter)}`
              : ""
          }`
        );
      } catch (err) {
        if (err.code === 26) {
          // Collection doesn't exist error code
          console.log(
            `MongoDB collection ${collectionName} does not exist, skipping...`
          );
        } else {
          console.error(
            `Error deleting from MongoDB collection ${collectionName}:`,
            err
          );
        }
      }
    }

    // Clean Redis Keys
    for (const collection of collections) {
      try {
        let collectionName;

        // Extract collection name for Redis pattern
        if (typeof collection === "object" && collection.name) {
          collectionName = collection.name;
        } else {
          collectionName = collection;
        }

        const pattern = `${collectionName}:*`;
        const keys = await redisClient.keys(pattern);
        if (keys.length > 0) {
          await redisClient.del(...keys);
          console.log(
            `Successfully deleted Redis keys with pattern: ${pattern}`
          );
        } else {
          console.log(`No Redis keys found with pattern: ${pattern}`);
        }
      } catch (err) {
        console.error(
          `Error deleting Redis keys for pattern ${collectionName}:*:`,
          err
        );
      }
    }

    console.log("All specified collections and Redis keys have been processed");
  } catch (err) {
    console.error("Error during cleanup:", err);
  } finally {
    await client.close();
    await redisClient.quit();
    console.log("Disconnected from MongoDB and Redis");
  }
}

module.exports = {
  cleanCollections,
};
