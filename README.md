# MionAI Server - <PERSON>je <PERSON>ı

## Proje Ne Yapıyor?

MionAI Server, **AI karakterler aracılığıyla ürün satışı yapan bir affiliate marketing platformu**. Ana amacı, farklı kişilik ve uzmanlık alanlarına sahip AI karakterler kullanarak kullanıcılara kişiselleştirilmiş ürün önerileri sunmak ve satış yapmak.

## Client'ın Kullanabileceği Özellikler

### 1. **Ürün Arama ve Öneriler - `chat.ts`**

- <PERSON><PERSON>, AI karakterlerle sohbet ederek ürün arama yapabiliyor
- Her karakter kendi kişiliğine göre ürün önerileri sunuyor
- **Product tags** ile ürün linkleri otomatik olarak entegre ediliyor
- Real-time SSE ile anlık ürün önerileri
- Karakterler farklı perspektiflerden ürün değerlendirmesi yapıyor (b<PERSON><PERSON><PERSON><PERSON>lı, ka<PERSON>l<PERSON>, vb.)

**API Endpoint:** `POST /c3/search-results`

### 2. **Blog Yazıları ve Ürün Entegrasyonu - `blog.ts`**

- Client, karakterlerin yazdığı blog yazılarını okuyabiliyor
- **Blog yazılarının ana amacı ürün satışı** - her yazıda product tags ile ürün linkleri var
- Karakterler kendi uzmanlık alanlarında blog yazıları yazıyor
- Her blog yazısı karakterin kişiliğine göre ürün önerileri içeriyor

**API Endpoints:**

- `GET /c3/blog-post` - Blog yazılarını listele
- `GET /c3/blog-post/:id` - Belirli blog yazısını görüntüle

### 3. **AI Karakterler ile İçerik Üretimi - `character.ts`**

- AI karakterler otomatik olarak blog yazıları yazıyor
- **Her karakter "yazar" gibi davranarak ürün satış odaklı içerik üretiyor**
- Karakterlerin kişiliklerine göre farklı ürün önerileri ve satış stratejileri
- Her karakter kendi uzmanlık alanında ürün değerlendirmesi yapıyor

**API Endpoint:** `POST /c3/blog-post`

## Karakter Sistemi ve Satış Stratejisi

- Her karakterin benzersiz kişiliği, uzmanlık alanı ve **satış yaklaşımı** var
- Karakterler farklı hedef kitlelere hitap ediyor:
  - **Bütçe odaklı karakterler:** Uygun fiyatlı ürünler, indirimler, değer odaklı öneriler
  - **Lüks odaklı karakterler:** Premium ürünler, kalite odaklı öneriler, özel özellikler
  - **Kırsal/şehirli karakterler:** Yaşam tarzına uygun ürünler
- Karakterler domain-independent (her konuda ürün önerisi yapabiliyor)
- **Her karakter kendi kişiliğine göre affiliate linkler ve ürün önerileri sunuyor**

## Prompt Sistemi ve Ürün Satış Stratejisi

`@generators/` klasöründe **ürün satışı odaklı** promptlar bulunuyor:

- `search-response-generator.md` - **Ürün arama ve önerileri için** (product tags zorunlu)
- `blog-post-generator.md` - **Ürün satış odaklı blog yazıları için** (her yazıda ürün önerileri)
- `description-generator.md` - **Ürün açıklamaları ve pazarlama metinleri için**
- `search-terms-generator.md` - **Ürün arama terimleri ve karakter seçimi için**

**Önemli:** Tüm promptlar ürün satışı ve affiliate marketing odaklı tasarlanmış.

## Teknik Özellikler ve Satış Altyapısı

- **Real-time Communication:** SSE ile anlık ürün önerileri
- **AI Integration:** Azure GPT, Google GenAI, Anthropic Claude ile ürün değerlendirmesi
- **Vector Search:** Ürün veritabanında akıllı arama ve eşleştirme
- **Multi-language Support:** Global pazarlara hitap eden çoklu dil desteği
- **Character-driven Content:** Kişilik odaklı ürün pazarlama ve satış stratejileri
- **Product Tags System:** Otomatik affiliate link entegrasyonu

## Proje Özeti

**MionAI Server, AI karakterler aracılığıyla ürün satışı yapan bir affiliate marketing platformudur.**

### Ana Amaç:

- **Ürün Satışı:** AI karakterler kullanarak kişiselleştirilmiş ürün önerileri ve satış
- **Affiliate Marketing:** Product tags sistemi ile otomatik affiliate link entegrasyonu
- **Karakter Odaklı Pazarlama:** Her karakterin kendi kişiliğine göre farklı satış stratejileri

### Nasıl Çalışıyor:

1. **Karakterler "yazar" gibi davranarak** ürün satış odaklı içerik üretiyor
2. **Her karakter kendi hedef kitlesine** uygun ürünler öneriyor (bütçe odaklı, lüks odaklı, vb.)
3. **Product tags sistemi** ile ürün linkleri otomatik olarak entegre ediliyor
4. **Vector search** ile akıllı ürün eşleştirmesi yapılıyor
5. **Global pazarlara** hitap eden çoklu dil desteği

**Sonuç:** Platform, AI karakterler aracılığıyla otomatik ürün satışı yapan bir affiliate marketing sistemi.
