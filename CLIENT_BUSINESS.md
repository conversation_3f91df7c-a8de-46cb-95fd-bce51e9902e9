# MionAI - Client Business Model

## Client Bu Platformu Ne İçin <PERSON>?

**MionAI, client'ların AI karakterler aracılığıyla ürün arama yapabileceği, blogları okuyabileceği bir platform.**

## Client'ın Ana Amacı

**<PERSON><PERSON><PERSON><PERSON> bul<PERSON>, satın almak ve karakterlerin blog yazılarını okumak.**

## Nasıl Çalışıyor?

### 1. **Client Blog Okuma Yapıyor**

- Belirli karakterlerin yazdığı blog yazılarını okuyor
- Karakterler belirli aralıklarla otomatik blog yazıyor
- Her blog yazısında ürün önerileri ve product tags var

### 2. **Client Soru Sorma Yapıyor**

- **İki seçenek var:**
  - Belirli bir karaktere soru soruyor
  - Ortaya soru soruyor (AI otomatik karakter seçiyor)
- "Ev dekorasyonu için ürü<PERSON> ö<PERSON>"
- "Spor ekipmanları bul"
- "Teknoloji ürünleri ara"

### 3. **AI Otomatik Olarak:**

- **Karakter seçiyor** (client seçmiyor!)
- Ülkeye uygun karakteri otomatik buluyor
- Karakterin kişiliğine göre ürün önerileri sunuyor
- **Cevaplar aynı formatta** (product tags ile)

### 4. **Karakterler Farklı Perspektiflerden Öneriyor:**

- **Bütçe odaklı karakter:** Ucuz ürünler, indirimler
- **Lüks odaklı karakter:** Premium ürünler, kalite
- **Kırsal karakter:** Köy yaşamına uygun ürünler
- **Şehirli karakter:** Modern yaşam ürünleri

### 5. **Client Ürün Satın Alıyor**

- AI karakterin önerdiği ürünleri görüyor
- Product tags ile ürün linklerine tıklıyor
- Satın alma yapıyor

## Client'ın Avantajları

✅ **Kişiselleştirilmiş Öneriler** - AI otomatik karakter seçiyor
✅ **Farklı Perspektifler** - Her karakter farklı yaklaşım
✅ **Otomatik İçerik** - Blog yazıları otomatik yazılıyor
✅ **Ürün Entegrasyonu** - Her öneride product tags
✅ **Çoklu Dil** - Global ürünlere erişim

## Client Ne Yapıyor?

1. **Platforma giriş yapıyor**
2. **İki seçenek var:**
   - **Blog okuma:** Karakterlerin yazdığı blog yazılarını okuyor
   - **Soru sorma:** Belirli karaktere veya ortaya soru soruyor
3. **AI otomatik olarak:**
   - Karakter seçiyor (eğer belirli karakter seçilmemişse)
   - Ürün önerileri sunuyor
   - Product tags ekliyor
4. **Client ürünleri görüyor ve satın alıyor**

## Sonuç

**MionAI = AI karakterler aracılığıyla ürün önerisi yapan platform**

- **Client:** Ürün arıyor ve satın alıyor
- **AI:** Karakter seçiyor ve içerik üretiyor
- **Karakterler:** Farklı perspektiflerden ürün öneriyor
- **Platform:** Ürün satışı yapıyor (affiliate marketing)
